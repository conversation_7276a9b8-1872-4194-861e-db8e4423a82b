//
//  ContentView.swift
//  Picture
//
//  Created by lincoo on 6/18/25.
//

import SwiftUI
import PhotosUI
import Vision
import CoreImage
import CoreImage.CIFilterBuiltins

struct ContentView: View {
    @State private var selectedImage: UIImage?
    @State private var processedImage: UIImage?
    @State private var isProcessing = false
    @State private var showingImagePicker = false
    @State private var showingActionSheet = false
    @State private var sourceType: UIImagePickerController.SourceType = .photoLibrary
    @State private var showingAlert = false
    @State private var alertMessage = ""

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("智能抠图")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding(.top)

                // 图片显示区域
                if let processedImage = processedImage {
                    VStack {
                        Text("抠图结果")
                            .font(.headline)
                            .foregroundColor(.secondary)

                        Image(uiImage: processedImage)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(maxHeight: 300)
                            .background(
                                // 棋盘格背景显示透明区域
                                CheckerboardView()
                            )
                            .cornerRadius(12)
                            .shadow(radius: 5)
                    }
                } else if let selectedImage = selectedImage {
                    VStack {
                        Text("原始图片")
                            .font(.headline)
                            .foregroundColor(.secondary)

                        Image(uiImage: selectedImage)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(maxHeight: 300)
                            .cornerRadius(12)
                            .shadow(radius: 5)
                    }
                } else {
                    // 占位符
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.gray.opacity(0.2))
                        .frame(height: 300)
                        .overlay(
                            VStack {
                                Image(systemName: "photo")
                                    .font(.system(size: 50))
                                    .foregroundColor(.gray)
                                Text("选择图片开始抠图")
                                    .font(.headline)
                                    .foregroundColor(.gray)
                            }
                        )
                }

                // 按钮区域
                VStack(spacing: 15) {
                    // 选择图片按钮
                    Button(action: {
                        showingActionSheet = true
                    }) {
                        HStack {
                            Image(systemName: "photo.on.rectangle")
                            Text("选择图片")
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .cornerRadius(12)
                    }

                    // 开始抠图按钮
                    Button(action: {
                        processImage()
                    }) {
                        HStack {
                            if isProcessing {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                                Text("处理中...")
                            } else {
                                Image(systemName: "scissors")
                                Text("开始抠图")
                            }
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(selectedImage != nil && !isProcessing ? Color.green : Color.gray)
                        .cornerRadius(12)
                    }
                    .disabled(selectedImage == nil || isProcessing)

                    // 保存图片按钮
                    if processedImage != nil {
                        Button(action: {
                            saveImage()
                        }) {
                            HStack {
                                Image(systemName: "square.and.arrow.down")
                                Text("保存到相册")
                            }
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.purple)
                            .cornerRadius(12)
                        }
                    }
                }
                .padding(.horizontal)

                Spacer()
            }
            .padding()
        }
        .actionSheet(isPresented: $showingActionSheet) {
            ActionSheet(
                title: Text("选择图片来源"),
                buttons: [
                    .default(Text("相册")) {
                        sourceType = .photoLibrary
                        showingImagePicker = true
                    },
                    .default(Text("相机")) {
                        sourceType = .camera
                        showingImagePicker = true
                    },
                    .cancel()
                ]
            )
        }
        .sheet(isPresented: $showingImagePicker) {
            ImagePicker(sourceType: sourceType) { image in
                selectedImage = image
                processedImage = nil // 重置处理后的图片
            }
        }
        .alert("提示", isPresented: $showingAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
    }

    // MARK: - 图片处理方法
    private func processImage() {
        guard let inputImage = selectedImage else { return }

        isProcessing = true

        // 使用Vision框架进行背景移除
        DispatchQueue.global(qos: .userInitiated).async {
            self.removeBackground(from: inputImage) { result in
                DispatchQueue.main.async {
                    self.isProcessing = false
                    switch result {
                    case .success(let processedImage):
                        self.processedImage = processedImage
                    case .failure(let error):
                        self.alertMessage = "抠图失败: \(error.localizedDescription)"
                        self.showingAlert = true
                    }
                }
            }
        }
    }

    private func removeBackground(from image: UIImage, completion: @escaping (Result<UIImage, Error>) -> Void) {
        guard let cgImage = image.cgImage else {
            completion(.failure(ImageProcessingError.invalidImage))
            return
        }

        // 首先尝试使用人像分割
        let personRequest = VNGeneratePersonSegmentationRequest { request, error in
            if let error = error {
                print("人像分割失败: \(error.localizedDescription)")
                // 如果人像分割失败，尝试使用前景实例分割
                self.tryForegroundInstanceMask(cgImage: cgImage, completion: completion)
                return
            }

            guard let result = request.results?.first as? VNPixelBufferObservation else {
                print("人像分割没有结果")
                // 如果没有结果，尝试使用前景实例分割
                self.tryForegroundInstanceMask(cgImage: cgImage, completion: completion)
                return
            }

            do {
                let maskedImage = try self.applyPersonMask(result, to: cgImage)
                print("人像分割成功")
                completion(.success(maskedImage))
            } catch {
                print("应用人像遮罩失败: \(error.localizedDescription)")
                // 如果应用遮罩失败，尝试使用前景实例分割
                self.tryForegroundInstanceMask(cgImage: cgImage, completion: completion)
            }
        }

        // 设置人像分割的质量级别
        personRequest.qualityLevel = .balanced
        personRequest.outputPixelFormat = kCVPixelFormatType_OneComponent8

        let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
        do {
            try handler.perform([personRequest])
        } catch {
            print("执行人像分割请求失败: \(error.localizedDescription)")
            // 如果人像分割完全失败，尝试使用前景实例分割
            self.tryForegroundInstanceMask(cgImage: cgImage, completion: completion)
        }
    }

    private func tryForegroundInstanceMask(cgImage: CGImage, completion: @escaping (Result<UIImage, Error>) -> Void) {
        let request = VNGenerateForegroundInstanceMaskRequest { request, error in
            if let error = error {
                print("前景实例分割失败: \(error.localizedDescription)")
                // 如果前景实例分割也失败，使用改进的抠图方法
                self.tryImprovedBackgroundRemoval(cgImage: cgImage, completion: completion)
                return
            }

            guard let results = request.results, !results.isEmpty else {
                print("前景实例分割没有结果")
                // 如果没有找到前景对象，使用改进的抠图方法
                self.tryImprovedBackgroundRemoval(cgImage: cgImage, completion: completion)
                return
            }

            // 尝试处理所有检测到的实例
            guard let bestResult = results.first as? VNInstanceMaskObservation else {
                print("无法获取前景实例遮罩")
                self.tryImprovedBackgroundRemoval(cgImage: cgImage, completion: completion)
                return
            }

            do {
                let maskedImage = try self.applyInstanceMask(bestResult, to: cgImage)
                print("前景实例分割成功")
                completion(.success(maskedImage))
            } catch {
                print("应用实例遮罩失败: \(error.localizedDescription)")
                // 如果应用遮罩失败，使用改进的抠图方法
                self.tryImprovedBackgroundRemoval(cgImage: cgImage, completion: completion)
            }
        }

        let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
        do {
            try handler.perform([request])
        } catch {
            print("执行前景实例分割请求失败: \(error.localizedDescription)")
            // 如果请求失败，使用改进的抠图方法
            self.tryImprovedBackgroundRemoval(cgImage: cgImage, completion: completion)
        }
    }

    private func tryImprovedBackgroundRemoval(cgImage: CGImage, completion: @escaping (Result<UIImage, Error>) -> Void) {
        print("使用改进的背景移除方法")

        // 使用更保守的方法：创建一个简单的中心区域遮罩
        let ciImage = CIImage(cgImage: cgImage)
        let imageSize = ciImage.extent

        // 创建一个椭圆形遮罩，假设主要对象在图像中心
        let centerX = imageSize.width / 2
        let centerY = imageSize.height / 2
        let radiusX = imageSize.width * 0.35  // 保守的椭圆半径
        let radiusY = imageSize.height * 0.45

        // 创建径向渐变遮罩
        guard let radialGradient = CIFilter(name: "CIRadialGradient") else {
            self.createSimpleCrop(cgImage: cgImage, completion: completion)
            return
        }

        radialGradient.setValue(CIVector(x: centerX, y: centerY), forKey: "inputCenter")
        radialGradient.setValue(min(radiusX, radiusY) * 0.6, forKey: "inputRadius0")  // 内圆半径
        radialGradient.setValue(min(radiusX, radiusY) * 1.2, forKey: "inputRadius1")  // 外圆半径
        radialGradient.setValue(CIColor.white, forKey: "inputColor0")  // 中心颜色（保留）
        radialGradient.setValue(CIColor.clear, forKey: "inputColor1")  // 边缘颜色（移除）

        guard let gradientImage = radialGradient.outputImage else {
            self.createSimpleCrop(cgImage: cgImage, completion: completion)
            return
        }

        // 裁剪渐变到图像尺寸
        let croppedGradient = gradientImage.cropped(to: imageSize)

        // 应用遮罩
        guard let blendFilter = CIFilter(name: "CIBlendWithMask") else {
            self.createSimpleCrop(cgImage: cgImage, completion: completion)
            return
        }

        blendFilter.setValue(ciImage, forKey: kCIInputImageKey)
        blendFilter.setValue(CIImage.clear, forKey: kCIInputBackgroundImageKey)
        blendFilter.setValue(croppedGradient, forKey: kCIInputMaskImageKey)

        guard let outputImage = blendFilter.outputImage else {
            self.createSimpleCrop(cgImage: cgImage, completion: completion)
            return
        }

        let context = CIContext()
        guard let cgOutputImage = context.createCGImage(outputImage, from: outputImage.extent) else {
            self.createSimpleCrop(cgImage: cgImage, completion: completion)
            return
        }

        print("改进的背景移除成功")
        completion(.success(UIImage(cgImage: cgOutputImage)))
    }

    private func createSimpleCrop(cgImage: CGImage, completion: @escaping (Result<UIImage, Error>) -> Void) {
        print("使用简单裁剪方法")

        // 创建一个简单的中心裁剪，保留图像的中心80%区域
        let originalSize = CGSize(width: cgImage.width, height: cgImage.height)
        let cropRatio: CGFloat = 0.8
        let newWidth = originalSize.width * cropRatio
        let newHeight = originalSize.height * cropRatio
        let x = (originalSize.width - newWidth) / 2
        let y = (originalSize.height - newHeight) / 2

        let cropRect = CGRect(x: x, y: y, width: newWidth, height: newHeight)

        guard let croppedCGImage = cgImage.cropping(to: cropRect) else {
            completion(.failure(ImageProcessingError.maskApplicationFailed))
            return
        }

        // 创建带透明背景的图像
        UIGraphicsBeginImageContextWithOptions(originalSize, false, 0.0)
        guard let context = UIGraphicsGetCurrentContext() else {
            completion(.failure(ImageProcessingError.maskApplicationFailed))
            return
        }

        // 设置透明背景
        context.clear(CGRect(origin: .zero, size: originalSize))

        // 在中心绘制裁剪后的图像
        let drawRect = CGRect(x: x, y: y, width: newWidth, height: newHeight)
        context.draw(croppedCGImage, in: drawRect)

        guard let resultImage = UIGraphicsGetImageFromCurrentImageContext() else {
            UIGraphicsEndImageContext()
            completion(.failure(ImageProcessingError.maskApplicationFailed))
            return
        }

        UIGraphicsEndImageContext()
        print("简单裁剪成功")
        completion(.success(resultImage))
    }

    private func createTransparentBackground(cgImage: CGImage, completion: @escaping (Result<UIImage, Error>) -> Void) {
        // 创建一个带有透明背景的图像
        let size = CGSize(width: cgImage.width, height: cgImage.height)

        UIGraphicsBeginImageContextWithOptions(size, false, 0.0)
        guard let context = UIGraphicsGetCurrentContext() else {
            completion(.failure(ImageProcessingError.maskApplicationFailed))
            return
        }

        // 设置透明背景
        context.clear(CGRect(origin: .zero, size: size))

        // 绘制原图，但稍微缩小以创建边框效果
        let insetRect = CGRect(x: size.width * 0.05, y: size.height * 0.05,
                              width: size.width * 0.9, height: size.height * 0.9)
        context.draw(cgImage, in: insetRect)

        guard let resultImage = UIGraphicsGetImageFromCurrentImageContext() else {
            UIGraphicsEndImageContext()
            completion(.failure(ImageProcessingError.maskApplicationFailed))
            return
        }

        UIGraphicsEndImageContext()
        completion(.success(resultImage))
    }

    private func applyPersonMask(_ mask: VNPixelBufferObservation, to image: CGImage) throws -> UIImage {
        let ciImage = CIImage(cgImage: image)
        let maskCIImage = CIImage(cvPixelBuffer: mask.pixelBuffer)

        print("原图尺寸: \(ciImage.extent)")
        print("遮罩尺寸: \(maskCIImage.extent)")

        // 调整mask尺寸以匹配原图
        let scaleX = ciImage.extent.width / maskCIImage.extent.width
        let scaleY = ciImage.extent.height / maskCIImage.extent.height
        let scaledMask = maskCIImage.transformed(by: CGAffineTransform(scaleX: scaleX, y: scaleY))

        // 简化遮罩验证 - 直接尝试应用遮罩
        let context = CIContext()

        // 应用mask
        guard let blendFilter = CIFilter(name: "CIBlendWithMask") else {
            throw ImageProcessingError.maskApplicationFailed
        }

        blendFilter.setValue(ciImage, forKey: kCIInputImageKey)
        blendFilter.setValue(CIImage.clear, forKey: kCIInputBackgroundImageKey)
        blendFilter.setValue(scaledMask, forKey: kCIInputMaskImageKey)

        guard let outputImage = blendFilter.outputImage else {
            throw ImageProcessingError.maskApplicationFailed
        }

        guard let cgOutputImage = context.createCGImage(outputImage, from: outputImage.extent) else {
            throw ImageProcessingError.maskApplicationFailed
        }

        return UIImage(cgImage: cgOutputImage)
    }

    private func applyInstanceMask(_ mask: VNInstanceMaskObservation, to image: CGImage) throws -> UIImage {
        // 获取mask的像素缓冲区
        let maskPixelBuffer = try mask.generateMaskedImage(ofInstances: mask.allInstances, from: VNImageRequestHandler(cgImage: image), croppedToInstancesExtent: false)
        let ciImage = CIImage(cgImage: image)
        let maskCIImage = CIImage(cvPixelBuffer: maskPixelBuffer)

        // 调整mask尺寸以匹配原图
        let scaleX = ciImage.extent.width / maskCIImage.extent.width
        let scaleY = ciImage.extent.height / maskCIImage.extent.height
        let scaledMask = maskCIImage.transformed(by: CGAffineTransform(scaleX: scaleX, y: scaleY))

        // 应用mask
        let filter = CIFilter.blendWithMask()
        filter.inputImage = ciImage
        filter.backgroundImage = CIImage.empty()
        filter.maskImage = scaledMask

        guard let outputImage = filter.outputImage else {
            throw ImageProcessingError.maskApplicationFailed
        }

        let context = CIContext()
        guard let cgOutputImage = context.createCGImage(outputImage, from: outputImage.extent) else {
            throw ImageProcessingError.maskApplicationFailed
        }

        return UIImage(cgImage: cgOutputImage)
    }

    private func saveImage() {
        guard let image = processedImage else { return }

        let imageSaver = ImageSaver { result in
            DispatchQueue.main.async {
                switch result {
                case .success:
                    self.alertMessage = "图片已保存到相册"
                case .failure(let error):
                    self.alertMessage = "保存失败: \(error.localizedDescription)"
                }
                self.showingAlert = true
            }
        }
        imageSaver.writeToPhotoAlbum(image: image)
    }
}

// MARK: - 错误类型
enum ImageProcessingError: LocalizedError {
    case invalidImage
    case noMaskFound
    case maskApplicationFailed

    var errorDescription: String? {
        switch self {
        case .invalidImage:
            return "无效的图片"
        case .noMaskFound:
            return "未找到前景对象"
        case .maskApplicationFailed:
            return "应用遮罩失败"
        }
    }
}

// MARK: - 图片保存器
class ImageSaver: NSObject {
    private let completion: (Result<Void, Error>) -> Void

    init(completion: @escaping (Result<Void, Error>) -> Void) {
        self.completion = completion
        super.init()
    }

    func writeToPhotoAlbum(image: UIImage) {
        UIImageWriteToSavedPhotosAlbum(image, self, #selector(saveCompleted), nil)
    }

    @objc func saveCompleted(_ image: UIImage, didFinishSavingWithError error: Error?, contextInfo: UnsafeRawPointer) {
        if let error = error {
            completion(.failure(error))
        } else {
            completion(.success(()))
        }
    }
}

// MARK: - 棋盘格背景视图
struct CheckerboardView: View {
    let squareSize: CGFloat = 20

    var body: some View {
        GeometryReader { geometry in
            let rows = Int(geometry.size.height / squareSize) + 1
            let columns = Int(geometry.size.width / squareSize) + 1

            VStack(spacing: 0) {
                ForEach(0..<rows, id: \.self) { row in
                    HStack(spacing: 0) {
                        ForEach(0..<columns, id: \.self) { column in
                            Rectangle()
                                .fill((row + column) % 2 == 0 ? Color.white : Color.gray.opacity(0.3))
                                .frame(width: squareSize, height: squareSize)
                        }
                    }
                }
            }
        }
    }
}

// MARK: - 图片选择器
struct ImagePicker: UIViewControllerRepresentable {
    let sourceType: UIImagePickerController.SourceType
    let onImagePicked: (UIImage) -> Void

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.sourceType = sourceType
        picker.delegate = context.coordinator
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker

        init(_ parent: ImagePicker) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.onImagePicked(image)
            }
            picker.dismiss(animated: true)
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            picker.dismiss(animated: true)
        }
    }
}

#Preview {
    ContentView()
}
