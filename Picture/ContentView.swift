//
//  ContentView.swift
//  Picture
//
//  Created by lincoo on 6/18/25.
//

import SwiftUI
import PhotosUI
import Vision
import CoreImage
import CoreImage.CIFilterBuiltins

struct ContentView: View {
    @State private var selectedImage: UIImage?
    @State private var processedImage: UIImage?
    @State private var isProcessing = false
    @State private var showingImagePicker = false
    @State private var showingActionSheet = false
    @State private var sourceType: UIImagePickerController.SourceType = .photoLibrary
    @State private var showingAlert = false
    @State private var alertMessage = ""

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("智能抠图")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding(.top)

                // 图片显示区域
                if let processedImage = processedImage {
                    VStack {
                        Text("抠图结果")
                            .font(.headline)
                            .foregroundColor(.secondary)

                        Image(uiImage: processedImage)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(maxHeight: 300)
                            .background(
                                // 棋盘格背景显示透明区域
                                CheckerboardView()
                            )
                            .cornerRadius(12)
                            .shadow(radius: 5)
                    }
                } else if let selectedImage = selectedImage {
                    VStack {
                        Text("原始图片")
                            .font(.headline)
                            .foregroundColor(.secondary)

                        Image(uiImage: selectedImage)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(maxHeight: 300)
                            .cornerRadius(12)
                            .shadow(radius: 5)
                    }
                } else {
                    // 占位符
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.gray.opacity(0.2))
                        .frame(height: 300)
                        .overlay(
                            VStack {
                                Image(systemName: "photo")
                                    .font(.system(size: 50))
                                    .foregroundColor(.gray)
                                Text("选择图片开始抠图")
                                    .font(.headline)
                                    .foregroundColor(.gray)
                            }
                        )
                }

                // 按钮区域
                VStack(spacing: 15) {
                    // 选择图片按钮
                    Button(action: {
                        showingActionSheet = true
                    }) {
                        HStack {
                            Image(systemName: "photo.on.rectangle")
                            Text("选择图片")
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .cornerRadius(12)
                    }

                    // 开始抠图按钮
                    Button(action: {
                        processImage()
                    }) {
                        HStack {
                            if isProcessing {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                                Text("处理中...")
                            } else {
                                Image(systemName: "scissors")
                                Text("开始抠图")
                            }
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(selectedImage != nil && !isProcessing ? Color.green : Color.gray)
                        .cornerRadius(12)
                    }
                    .disabled(selectedImage == nil || isProcessing)

                    // 保存图片按钮
                    if processedImage != nil {
                        Button(action: {
                            saveImage()
                        }) {
                            HStack {
                                Image(systemName: "square.and.arrow.down")
                                Text("保存到相册")
                            }
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.purple)
                            .cornerRadius(12)
                        }
                    }
                }
                .padding(.horizontal)

                Spacer()
            }
            .padding()
        }
        .actionSheet(isPresented: $showingActionSheet) {
            ActionSheet(
                title: Text("选择图片来源"),
                buttons: [
                    .default(Text("相册")) {
                        sourceType = .photoLibrary
                        showingImagePicker = true
                    },
                    .default(Text("相机")) {
                        sourceType = .camera
                        showingImagePicker = true
                    },
                    .cancel()
                ]
            )
        }
        .sheet(isPresented: $showingImagePicker) {
            ImagePicker(sourceType: sourceType) { image in
                selectedImage = image
                processedImage = nil // 重置处理后的图片
            }
        }
        .alert("提示", isPresented: $showingAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
    }

    // MARK: - 图片处理方法
    private func processImage() {
        guard let inputImage = selectedImage else { return }

        isProcessing = true

        // 使用Vision框架进行背景移除
        DispatchQueue.global(qos: .userInitiated).async {
            self.removeBackground(from: inputImage) { result in
                DispatchQueue.main.async {
                    self.isProcessing = false
                    switch result {
                    case .success(let processedImage):
                        self.processedImage = processedImage
                    case .failure(let error):
                        self.alertMessage = "抠图失败: \(error.localizedDescription)"
                        self.showingAlert = true
                    }
                }
            }
        }
    }

    private func removeBackground(from image: UIImage, completion: @escaping (Result<UIImage, Error>) -> Void) {
        guard let cgImage = image.cgImage else {
            completion(.failure(ImageProcessingError.invalidImage))
            return
        }

        let request = VNGenerateForegroundInstanceMaskRequest { request, error in
            if let error = error {
                completion(.failure(error))
                return
            }

            guard let result = request.results?.first as? VNInstanceMaskObservation else {
                completion(.failure(ImageProcessingError.noMaskFound))
                return
            }

            do {
                let maskedImage = try self.applyMask(result, to: cgImage)
                completion(.success(maskedImage))
            } catch {
                completion(.failure(error))
            }
        }

        let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
        do {
            try handler.perform([request])
        } catch {
            completion(.failure(error))
        }
    }

    private func applyMask(_ mask: VNInstanceMaskObservation, to image: CGImage) throws -> UIImage {
        // 获取mask的像素缓冲区
        let maskPixelBuffer = try mask.generateMaskedImage(ofInstances: mask.allInstances, from: VNImageRequestHandler(cgImage: image), croppedToInstancesExtent: false)
        let ciImage = CIImage(cgImage: image)
        let maskCIImage = CIImage(cvPixelBuffer: maskPixelBuffer)

        // 调整mask尺寸以匹配原图
        let scaleX = ciImage.extent.width / maskCIImage.extent.width
        let scaleY = ciImage.extent.height / maskCIImage.extent.height
        let scaledMask = maskCIImage.transformed(by: CGAffineTransform(scaleX: scaleX, y: scaleY))

        // 应用mask
        let filter = CIFilter.blendWithMask()
        filter.inputImage = ciImage
        filter.backgroundImage = CIImage.empty()
        filter.maskImage = scaledMask

        guard let outputImage = filter.outputImage else {
            throw ImageProcessingError.maskApplicationFailed
        }

        let context = CIContext()
        guard let cgOutputImage = context.createCGImage(outputImage, from: outputImage.extent) else {
            throw ImageProcessingError.maskApplicationFailed
        }

        return UIImage(cgImage: cgOutputImage)
    }

    private func saveImage() {
        guard let image = processedImage else { return }

        let imageSaver = ImageSaver { result in
            DispatchQueue.main.async {
                switch result {
                case .success:
                    self.alertMessage = "图片已保存到相册"
                case .failure(let error):
                    self.alertMessage = "保存失败: \(error.localizedDescription)"
                }
                self.showingAlert = true
            }
        }
        imageSaver.writeToPhotoAlbum(image: image)
    }
}

// MARK: - 错误类型
enum ImageProcessingError: LocalizedError {
    case invalidImage
    case noMaskFound
    case maskApplicationFailed

    var errorDescription: String? {
        switch self {
        case .invalidImage:
            return "无效的图片"
        case .noMaskFound:
            return "未找到前景对象"
        case .maskApplicationFailed:
            return "应用遮罩失败"
        }
    }
}

// MARK: - 图片保存器
class ImageSaver: NSObject {
    private let completion: (Result<Void, Error>) -> Void

    init(completion: @escaping (Result<Void, Error>) -> Void) {
        self.completion = completion
        super.init()
    }

    func writeToPhotoAlbum(image: UIImage) {
        UIImageWriteToSavedPhotosAlbum(image, self, #selector(saveCompleted), nil)
    }

    @objc func saveCompleted(_ image: UIImage, didFinishSavingWithError error: Error?, contextInfo: UnsafeRawPointer) {
        if let error = error {
            completion(.failure(error))
        } else {
            completion(.success(()))
        }
    }
}

// MARK: - 棋盘格背景视图
struct CheckerboardView: View {
    let squareSize: CGFloat = 20

    var body: some View {
        GeometryReader { geometry in
            let rows = Int(geometry.size.height / squareSize) + 1
            let columns = Int(geometry.size.width / squareSize) + 1

            VStack(spacing: 0) {
                ForEach(0..<rows, id: \.self) { row in
                    HStack(spacing: 0) {
                        ForEach(0..<columns, id: \.self) { column in
                            Rectangle()
                                .fill((row + column) % 2 == 0 ? Color.white : Color.gray.opacity(0.3))
                                .frame(width: squareSize, height: squareSize)
                        }
                    }
                }
            }
        }
    }
}

// MARK: - 图片选择器
struct ImagePicker: UIViewControllerRepresentable {
    let sourceType: UIImagePickerController.SourceType
    let onImagePicked: (UIImage) -> Void

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.sourceType = sourceType
        picker.delegate = context.coordinator
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker

        init(_ parent: ImagePicker) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.onImagePicked(image)
            }
            picker.dismiss(animated: true)
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            picker.dismiss(animated: true)
        }
    }
}

#Preview {
    ContentView()
}
